[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "iicrawlermcp"
version = "0.1.0"
description = "A LangChain-based web crawler with Playwright integration"
readme = "README.md"
requires-python = ">=3.11"
license = {text = "MIT"}
authors = [
    {name = "iICrawlerMCP Team"}
]
keywords = ["web-crawling", "langchain", "playwright", "automation"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Internet :: WWW/HTTP :: Browsers",
    "Topic :: Software Development :: Libraries :: Python Modules",
]

dependencies = [
    "langchain>=0.1.0",
    "langchain-community>=0.0.20",
    "langchain-openai>=0.0.5",
    "playwright>=1.40.0",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
]

[project.optional-dependencies]
mcp = [
    "mcp>=1.0.0",
]

[project.urls]
Homepage = "https://github.com/your-username/iicrawlermcp"
Repository = "https://github.com/your-username/iicrawlermcp"
Issues = "https://github.com/your-username/iicrawlermcp/issues"

[project.scripts]
iicrawlermcp = "src.iicrawlermcp.main:main"
iicrawlermcp-mcp = "scripts.start_mcp_server:main"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
pythonpath = ["src"]
